# AES and AES-CMAC Textbook Implementation

AES(-128, -192, -256) and AES-CMAC implementation in C.

> [!NOTE]
> This is a **textbook implementation** that is not optimized for performance or security.

## Reference

- AES, [Federal Information Processing Standards Publication](https://nvlpubs.nist.gov/nistpubs/fips/nist.fips.197.pdf).
- The AES-CMAC Algorithm, [RFC 4493](https://tools.ietf.org/html/rfc4493).

## License

[MIT](https://github.com/megrxu/AES-CMAC/blob/master/LICENSE)